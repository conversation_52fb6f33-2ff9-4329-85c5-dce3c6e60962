import { AppDataSource } from '../connection';
import { Sale } from '../models/Sale';
import { SaleItem } from '../models/SaleItem';
import { PetService } from '../models/PetService';
import { Product } from '../models/Product';
import { Customer } from '../models/Customer';
import { Service } from '../models/Service';
import { Between } from 'typeorm';

// Input interfaces for type safety
interface SaleInput {
  customerId?: number;
  total_amount?: number;
  payment_method?: string;
  sale_date?: Date;
  customer?: any;
  status?: string;
}

interface SaleItemInput {
  productId?: number | null;
  serviceId?: number | null;
  quantity?: number;
  price_per_unit?: number;
  product?: any;
  service?: any;
  sale?: any;
  pet_id?: number;
  petId?: number;
  customer_package_id?: number | null;
  is_package_service?: boolean;
}

export class SaleService {
  private saleRepository = AppDataSource.getRepository(Sale);
  private saleItemRepository = AppDataSource.getRepository(SaleItem);
  private petServiceRepository = AppDataSource.getRepository(PetService);

  async findAll(): Promise<Sale[]> {
    return this.saleRepository.find({
      relations: ['customer'],
      order: { sale_date: 'DESC' }
    });
  }

  async findById(id: number): Promise<Sale | null> {
    return this.saleRepository.findOne({
      where: { id },
      relations: ['customer']
    });
  }

  async findByCustomerId(customerId: number): Promise<Sale[]> {
    return this.saleRepository.find({
      where: { customer: { id: customerId } },
      relations: ['customer'],
      order: { sale_date: 'DESC' }
    });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Sale[]> {
    return this.saleRepository.find({
      where: {
        sale_date: Between(startDate, endDate)
      },
      relations: ['customer'],
      order: { sale_date: 'DESC' }
    });
  }

  async getSaleItems(saleId: number): Promise<SaleItem[]> {
    console.log(`[SaleService] Fetching items for sale with ID: ${saleId}`);
    try {
      const items = await this.saleItemRepository.find({
        where: { sale: { id: saleId } },
        relations: ['product', 'service']
      });
      console.log(`[SaleService] Found ${items.length} items for sale ${saleId}`);
      return items;
    } catch (error) {
      console.error(`[SaleService] Error fetching items for sale ${saleId}:`, error);
      throw error;
    }
  }

  async create(saleData: SaleInput, items: SaleItemInput[]): Promise<Sale | null> {
    // Start a transaction
    return AppDataSource.transaction(async (transactionalEntityManager) => {
      // First, validate inventory levels before proceeding with the sale
      for (const itemData of items) {
        // Only check inventory for products, not services
        if (itemData.productId && itemData.quantity) {
          const productRepo = transactionalEntityManager.getRepository(Product);
          const product = await productRepo.findOne({ where: { id: itemData.productId } });

          // If the product exists, check if there's enough inventory
          if (product) {
            if (product.stock_quantity < itemData.quantity) {
              // Not enough inventory - throw an error to abort the transaction
              throw new Error(
                `Insufficient inventory for product "${product.name}". ` +
                `Requested: ${itemData.quantity}, Available: ${product.stock_quantity}`
              );
            }
          }
        }
      }

      // If we get here, we have sufficient inventory for all products
      // First, check if we have a customerId and set up the customer relationship
      let saleWithRelations = { ...saleData };

      if (saleData.customerId) {
        const customerRepo = transactionalEntityManager.getRepository(Customer);
        const customer = await customerRepo.findOne({ where: { id: saleData.customerId } });
        if (customer) {
          saleWithRelations.customer = customer;
        }
      }

      // Create and save the sale
      const sale = transactionalEntityManager.create(Sale, saleWithRelations);
      const savedSale = await transactionalEntityManager.save(sale);

      // Create and save each sale item
      for (const itemData of items) {
        let productToUpdate = null;
        let serviceEntity = null;

        // Convert any productId/serviceId to proper relationships
        if (!itemData.product && itemData.productId) {
          const productRepo = transactionalEntityManager.getRepository(Product);
          const product = await productRepo.findOne({ where: { id: itemData.productId } });
          if (product) {
            itemData.product = product;
            productToUpdate = product;
          }
        } else if (itemData.product) {
          productToUpdate = itemData.product;
        }

        if (!itemData.service && itemData.serviceId) {
          const serviceRepo = transactionalEntityManager.getRepository(Service);
          const service = await serviceRepo.findOne({ where: { id: itemData.serviceId } });
          if (service) {
            itemData.service = service;
            serviceEntity = service;
          }
        } else if (itemData.service) {
          serviceEntity = itemData.service;
        }

        // Check if this is a package service item
        const isPackageService = !!itemData.customer_package_id || !!itemData.is_package_service;

        const saleItem = transactionalEntityManager.create(SaleItem, {
          ...itemData,
          sale: savedSale,
          customer_package_id: itemData.customer_package_id || null,
          is_package_service: isPackageService
        });
        await transactionalEntityManager.save(saleItem);

        // If it's a product (not a service), update the stock
        if (productToUpdate && itemData.quantity) {
          const productRepo = transactionalEntityManager.getRepository(Product);
          console.log(`Updating stock for product #${productToUpdate.id}: Decreasing by ${itemData.quantity}`);
          await productRepo.decrement(
            { id: productToUpdate.id },
            'stock_quantity',
            itemData.quantity
          );
        }

        // If it's a service with a pet_id, record it in the pet's service history
        if (serviceEntity && (itemData.pet_id || itemData.petId)) {
          try {
            // Use either pet_id or petId, whichever is provided
            const petId = itemData.pet_id || itemData.petId;
            console.log(`DEBUG: Recording service #${serviceEntity.id} (${serviceEntity.name}) for pet #${petId}`);
            console.log('DEBUG: Raw item data:', JSON.stringify(itemData));

            // Update the sale item to include the pet ID
            if (saleItem.id) {
              await transactionalEntityManager.update(SaleItem, saleItem.id, { petId });
              console.log(`DEBUG: Updated sale item #${saleItem.id} with petId: ${petId}`);
            }

            // Create a new pet service record
            const petService = transactionalEntityManager.create(PetService, {
              pet_id: petId,
              service_name: serviceEntity.name,
              service_date: new Date(),
              notes: `Serviço adicionado via venda #${savedSale.id}`,
              price_paid: saleItem.price_per_unit,
              source_type: 'sale',
              source_id: saleItem.id
            });

            console.log('DEBUG: Created pet service record:', JSON.stringify(petService));
            const savedPetService = await transactionalEntityManager.save(petService);
            console.log(`DEBUG: Saved pet service with ID: ${savedPetService.id}`);

            // Verify the pet service was created correctly
            const checkService = await transactionalEntityManager.findOne(PetService, {
              where: { id: savedPetService.id }
            });
            console.log('DEBUG: Verified pet service in DB:', JSON.stringify(checkService));
          } catch (error) {
            console.error('Error recording pet service:', error);
            // Don't throw the error - we don't want to fail the whole transaction
            // just because we couldn't record the pet service
          }
        }
      }

      return this.findById(savedSale.id);
    });
  }

  async delete(id: number, withRestocking: boolean = true): Promise<boolean> {
    // First get the sale items to restore product stock if needed
    const items = await this.getSaleItems(id);

    return AppDataSource.transaction(async (transactionalEntityManager) => {
      // Restore product stock for each product in the sale, but only if withRestocking is true
      if (withRestocking) {
        for (const item of items) {
          let productId = null;

          // Check if we have a product object
          if (item.product) {
            productId = item.product.id;
          }

          if (productId && item.quantity) {
            const productRepo = transactionalEntityManager.getRepository(Product);
            console.log(`Restoring stock for product #${productId}: Increasing by ${item.quantity}`);
            await productRepo.increment(
              { id: productId },
              'stock_quantity',
              item.quantity
            );
          }
        }
      } else {
        console.log(`Deleting sale #${id} without restocking inventory`);
      }

      // Delete all sale items for this sale
      await this.saleItemRepository.delete({ sale: { id } });

      // Delete the sale itself
      const result = await this.saleRepository.delete(id);
      return result.affected !== undefined && result.affected !== null && result.affected > 0;
    });
  }

  async updateStatus(id: number, status: string): Promise<Sale | null> {
    try {
      await this.saleRepository.update(id, { status });
      return this.findById(id);
    } catch (error) {
      console.error(`Error updating status for sale with id ${id}:`, error);
      throw error;
    }
  }

  /**
   * Get all pending sales for a specific customer
   */
  async getPendingByCustomerId(customerId: number): Promise<Sale[]> {
    try {
      const sales = await this.saleRepository.find({
        where: {
          customer: { id: customerId },
          status: 'pending'
        },
        relations: ['items', 'items.product', 'items.service', 'customer']
      });

      return sales;
    } catch (error) {
      console.error('Error getting pending sales by customer:', error);
      throw error;
    }
  }
}