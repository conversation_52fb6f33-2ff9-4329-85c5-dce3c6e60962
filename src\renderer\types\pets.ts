export interface Pet {
  id: number;
  customer_id: number;
  name: string;
  type: string;
  breed: string | null;
  age: number | null;
  size: 'Pequeno' | 'Médio' | 'Grande' | null;
  gender: 'Macho' | 'Fêmea' | null;
  fur_type: 'Curto' | '<PERSON>é<PERSON>' | '<PERSON><PERSON>' | null;
  additional_notes: string | null;
  photo_url: string | null;
  is_hidden: boolean;
  status: 'active' | 'inactive';
  created_at: string | Date;
  updated_at: string | Date;
  customer?: Customer;
}

export interface Customer {
  id: number;
  name: string;
  email: string | null;
  phone: string | null;
}

export interface PetFormData {
  customer_id: number;
  name: string;
  type: string;
  breed: string;
  age: number | null;
  size: 'Pequeno' | 'Médio' | 'Grande' | '';
  gender: 'Macho' | 'Fêmea' | '';
  fur_type: 'Curto' | 'Médio' | 'Longo' | '';
  additional_notes: string;
  photo_url: string | null;
}

export interface PetService {
  id: number | string;
  pet_id: number;
  service_name: string;
  service_date: string;
  notes: string | null;
  price_paid?: number | null;
  source_type?: 'sale' | 'package' | 'unknown' | null;
  source_id?: number | null;
}

export type PetType = 'Cachorro' | 'Gato' | 'Outro';

export const PetTypes: PetType[] = [
  'Cachorro', 'Gato', 'Outro'
];