import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPriceFieldsToPetServices1734000000000 implements MigrationInterface {
    name = 'AddPriceFieldsToPetServices1734000000000';

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add new columns to pet_services table
        await queryRunner.query(`
            ALTER TABLE "pet_services" 
            ADD COLUMN "price_paid" DECIMAL(10,2) NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE "pet_services" 
            ADD COLUMN "source_type" VARCHAR NULL
        `);
        
        await queryRunner.query(`
            ALTER TABLE "pet_services" 
            ADD COLUMN "source_id" INTEGER NULL
        `);

        // Try to populate existing records with price information from sales
        // This is a best-effort migration - some records may not have price data
        await queryRunner.query(`
            UPDATE "pet_services" 
            SET 
                "price_paid" = (
                    SELECT si.price_per_unit 
                    FROM sale_items si 
                    INNER JOIN sales s ON si.sale_id = s.id 
                    INNER JOIN services srv ON si.service_id = srv.id
                    WHERE si.petId = pet_services.pet_id 
                    AND srv.name = pet_services.service_name
                    AND DATE(s.sale_date) = DATE(pet_services.service_date)
                    LIMIT 1
                ),
                "source_type" = 'sale',
                "source_id" = (
                    SELECT si.id 
                    FROM sale_items si 
                    INNER JOIN sales s ON si.sale_id = s.id 
                    INNER JOIN services srv ON si.service_id = srv.id
                    WHERE si.petId = pet_services.pet_id 
                    AND srv.name = pet_services.service_name
                    AND DATE(s.sale_date) = DATE(pet_services.service_date)
                    LIMIT 1
                )
            WHERE EXISTS (
                SELECT 1 
                FROM sale_items si 
                INNER JOIN sales s ON si.sale_id = s.id 
                INNER JOIN services srv ON si.service_id = srv.id
                WHERE si.petId = pet_services.pet_id 
                AND srv.name = pet_services.service_name
                AND DATE(s.sale_date) = DATE(pet_services.service_date)
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the added columns
        await queryRunner.query(`ALTER TABLE "pet_services" DROP COLUMN "source_id"`);
        await queryRunner.query(`ALTER TABLE "pet_services" DROP COLUMN "source_type"`);
        await queryRunner.query(`ALTER TABLE "pet_services" DROP COLUMN "price_paid"`);
    }
}
