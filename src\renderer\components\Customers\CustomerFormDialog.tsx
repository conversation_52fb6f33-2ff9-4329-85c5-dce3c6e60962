import React, { useState, useEffect } from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import FormHelperText from '@mui/material/FormHelperText';
import Alert from '@mui/material/Alert';
import Box from '@mui/material/Box';

import { CustomerFormData, Customer } from '../../types/customers';

interface CustomerFormDialogProps {
  open: boolean;
  onClose: () => void;
  onSave: (customerData: CustomerFormData) => void;
  customer?: Customer;
  title: string;
}

export const CustomerFormDialog: React.FC<CustomerFormDialogProps> = ({
  open,
  onClose,
  onSave,
  customer,
  title
}) => {
  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    additional_notes: ''
  });
  const [error, setError] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<keyof CustomerFormData, string | null>>({
    name: null,
    email: null,
    phone: null,
    address: null,
    additional_notes: null
  });

  useEffect(() => {
    if (customer) {
      setFormData({
        name: customer.name,
        email: customer.email || '',
        phone: customer.phone || '',
        address: customer.address || '',
        additional_notes: customer.additional_notes || ''
      });
    } else {
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        additional_notes: ''
      });
    }
    setError(null);
  }, [customer]);

  const handleChange = (field: keyof CustomerFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData({ ...formData, [field]: event.target.value });
  };

  const handleSubmit = async () => {
    // Reset errors
    setErrors({
      name: null,
      email: null,
      phone: null,
      address: null,
      additional_notes: null
    });
    setError(null);

    // Validate form
    let hasErrors = false;

    // Check if name is filled and has at least 3 characters
    if (!formData.name.trim()) {
      setErrors(prev => ({ ...prev, name: 'Nome é obrigatório' }));
      hasErrors = true;
    } else if (formData.name.trim().length < 3) {
      setErrors(prev => ({ ...prev, name: 'Nome deve ter pelo menos 3 caracteres' }));
      hasErrors = true;
    }

    // Validate email format if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setErrors(prev => ({ ...prev, email: 'Email inválido' }));
      hasErrors = true;
    }

    if (hasErrors) {
      return;
    }

    try {
      // Create a cleaned version of the form data where empty strings are properly handled
      // Always trim the values and convert empty results to empty strings for backend processing
      const cleanedFormData = {
        ...formData,
        name: formData.name.trim(),
        email: (formData.email || '').trim(),
        phone: (formData.phone || '').trim(),
        address: (formData.address || '').trim(),
        additional_notes: (formData.additional_notes || '').trim(),
      };

      console.log('[CustomerFormDialog] Submitting cleaned form data:', cleanedFormData);

      await onSave(cleanedFormData);
      // If successful, the parent component will close the dialog
    } catch (error: any) {
      console.error('Error saving customer:', error);
      // Display the error message from the server
      setError(error.message || 'Erro ao salvar cliente. Tente novamente.');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {customer ? 'Editar Cliente' : 'Adicionar Novo Cliente'}
      </DialogTitle>
      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Nome"
              value={formData.name}
              onChange={handleChange('name')}
              required
              error={!!errors.name}
              helperText={errors.name || ''}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Email"
              value={formData.email}
              onChange={handleChange('email')}
              type="email"
              error={!!errors.email}
              helperText={errors.email || ''}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Telefone"
              value={formData.phone}
              onChange={handleChange('phone')}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Endereço"
              value={formData.address}
              onChange={handleChange('address')}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Observações"
              value={formData.additional_notes}
              onChange={handleChange('additional_notes')}
              multiline
              rows={4}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancelar</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {customer ? 'Salvar' : 'Adicionar'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};